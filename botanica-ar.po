# Arabic translation for Botanica theme
# Copyright (C) 2024 Rising Bamboo
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Botanica 1.1.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/botanica\n"
"POT-Creation-Date: 2024-06-28 04:55:49+00:00\n"
"PO-Revision-Date: 2025-01-10 12:00+0000\n"
"Last-Translator: Arabic Translator\n"
"Language-Team: Arabic\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"X-Generator: Manual Translation\n"
"Text-Domain: botanica\n"

#: comments.php:33 comments.php:35
#: template-parts/contents/content-search.php:54
#: template-parts/contents/content-search.php:57
#: template-parts/contents/layouts/category/default.php:48
#: template-parts/contents/layouts/category/default.php:51
#: template-parts/contents/layouts/category/style-1.php:49
#: template-parts/contents/layouts/category/style-1.php:52
msgid " Comments"
msgstr " تعليقات"

#: comments.php:54
msgid "Comments are closed."
msgstr "التعليقات مغلقة."

#: functions.php:67
#. translators: 1: TGMA url, 2: Theme setup url
msgid ""
"Please install <a href=\"%1$s\"> Rising Bamboo Core and the required "
"plugins </a> first! <br/><br/> Alternatively, you can run <a "
"href=\"%2$s\">Theme Setup</a> to easily install all the plugins and data "
"necessary for the theme."
msgstr ""
"يرجى تثبيت <a href=\"%1$s\"> Rising Bamboo Core والإضافات المطلوبة </a> أولاً! <br/><br/> "
"بدلاً من ذلك، يمكنك تشغيل <a href=\"%2$s\">إعداد القالب</a> لتثبيت جميع الإضافات والبيانات "
"اللازمة للقالب بسهولة."

#: header.php:32
msgid "Skip to content"
msgstr "انتقل إلى المحتوى"

#: inc/app/class-setup.php:92
msgid "View your website"
msgstr "عرض موقعك الإلكتروني"

#: inc/app/class-setup.php:95
msgid "Import demo data"
msgstr "استيراد البيانات التجريبية"

#: inc/app/class-setup.php:264
msgid "Start replace url"
msgstr "بدء استبدال الرابط"

#: inc/app/class-setup.php:267
msgid "Import file path : "
msgstr "مسار ملف الاستيراد : "

#: inc/app/class-setup.php:275
msgid "Urls : "
msgstr "الروابط : "

#: inc/app/class-setup.php:294
msgid "Sql replace : "
msgstr "استبدال SQL : "

#: inc/app/class-setup.php:297 inc/app/class-setup.php:314
msgid "dbDelta result : "
msgstr "نتيجة dbDelta : "

#: inc/app/class-setup.php:320
msgid "End replace url"
msgstr "انتهاء استبدال الرابط"

#: inc/app/class-setup.php:352
msgid "Update term count"
msgstr "تحديث عدد المصطلحات"

#: inc/app/class-setup.php:365
msgid "Rows affected"
msgstr "الصفوف المتأثرة"

#: inc/app/class-setup.php:366
msgid "End update term count"
msgstr "انتهاء تحديث عدد المصطلحات"

#: inc/app/class-setup.php:377
msgid "Update comment count"
msgstr "تحديث عدد التعليقات"

#: inc/app/class-setup.php:385
msgid "# End update comment count"
msgstr "# انتهاء تحديث عدد التعليقات"

#: inc/app/class-setup.php:398
msgid "Update Woocommerce Default Pages"
msgstr "تحديث صفحات ووكومرس الافتراضية"

#: inc/app/class-setup.php:411
msgid "Update Shop Page"
msgstr "تحديث صفحة المتجر"

#: inc/app/class-setup.php:425
msgid "Update Cart Page"
msgstr "تحديث صفحة السلة"

#: inc/app/class-setup.php:440
msgid "Update Checkout Page"
msgstr "تحديث صفحة الدفع"

#: inc/app/class-setup.php:454
msgid "Update Account Page"
msgstr "تحديث صفحة الحساب"

#: inc/app/class-setup.php:457
msgid "Not update woocommerce pages"
msgstr "عدم تحديث صفحات ووكومرس"

#: inc/app/class-setup.php:479
msgid "Start update product lookup tables"
msgstr "بدء تحديث جداول البحث عن المنتجات"

#: inc/app/class-setup.php:492
msgid "Update woocommerce product attribute type"
msgstr "تحديث نوع خاصية منتج ووكومرس"

#: inc/app/class-setup.php:496
msgid "Result : "
msgstr "النتيجة : "

#: inc/app/class-setup.php:498
msgid "Clear all cache : "
msgstr "مسح جميع التخزين المؤقت : "

#: inc/app/class-setup.php:500
msgid "Delete woocommerce attribute taxonomies transient : "
msgstr "حذف تصنيفات خصائص ووكومرس المؤقتة : "

#: inc/app/class-setup.php:501
msgid "# End update woocommerce product attribute type"
msgstr "# انتهاء تحديث نوع خاصية منتج ووكومرس"

#: inc/app/class-setup.php:518
msgid "Create missing woocommerce pages"
msgstr "إنشاء صفحات ووكومرس المفقودة"

#: inc/app/class-setup.php:538
msgid "Log remapped term"
msgstr "سجل المصطلح المعاد تعيينه"

#: inc/app/menu/class-menu.php:65
msgid ""
"Please assign a menu to the primary location : Appearance > Menus > Manage "
"Locations"
msgstr ""
"يرجى تعيين قائمة للموقع الأساسي : المظهر > القوائم > إدارة المواقع"

#: inc/app/menu/class-menu.php:119
msgid ""
"Please assign a menu to the account location : Appearance > Menus > Manage "
"Locations"
msgstr ""
"يرجى تعيين قائمة لموقع الحساب : المظهر > القوائم > إدارة المواقع"

#: inc/app/menu/class-nav-menu-item.php:47
msgid "Icon Class"
msgstr "فئة الأيقونة"

#: inc/config/theme-customizer-default.php:420
msgid "Guarantee safe & secure checkout"
msgstr "ضمان دفع آمن ومحمي"

#: inc/config/theme-customizer-default.php:427
msgid "Ask a Question"
msgstr "اطرح سؤالاً"

#: inc/config/theme-menu-location.php:12
msgid "Primary Menu"
msgstr "القائمة الأساسية"

#: inc/config/theme-menu-location.php:13
msgid "Account Menu"
msgstr "قائمة الحساب"

#: inc/config/theme-plugin-required.php:10
msgid "Kirki"
msgstr "Kirki"

#: inc/config/theme-plugin-required.php:16
msgid "Elementor"
msgstr "Elementor"

#: inc/config/theme-plugin-required.php:22
msgid "Rising Bamboo Core"
msgstr "Rising Bamboo Core"

#: inc/config/theme-plugin-required.php:32
msgid "One Click Demo Import"
msgstr "استيراد العرض التوضيحي بنقرة واحدة"

#: inc/config/theme-plugin-required.php:37
msgid "WooCommerce"
msgstr "ووكومرس"

#: inc/config/theme-plugin-required.php:42
msgid "WPC Smart Wishlist for WooCommerce"
msgstr "قائمة الأمنيات الذكية WPC لووكومرس"

#: inc/config/theme-plugin-required.php:47
msgid "WPC Smart Compare for WooCommerce"
msgstr "المقارنة الذكية WPC لووكومرس"

#: inc/config/theme-plugin-required.php:52
msgid "Contact Form 7"
msgstr "نموذج الاتصال 7"

#: inc/config/theme-setup-wizard-import.php:11
msgid "Essential"
msgstr "أساسي"

#: inc/config/theme-setup-wizard-import.php:18
msgid "Extra Data ( Post, Product, Menu ...)"
msgstr "بيانات إضافية ( مقال، منتج، قائمة ...)"

#: inc/config/theme-setup-wizard.php:27
msgid "Theme Setup"
msgstr "إعداد القالب"

#: inc/config/theme-setup-wizard.php:30
#. translators: 1: Title Tag 2: Theme Name 3: Closing Title Tag
msgid "%1$s%2$s Themes &lsaquo; Theme Setup: %3$s%4$s"
msgstr "%1$s%2$s قوالب &lsaquo; إعداد القالب: %3$s%4$s"

#: inc/config/theme-setup-wizard.php:31
msgid "Return to the dashboard"
msgstr "العودة إلى لوحة التحكم"

#: inc/config/theme-setup-wizard.php:32
msgid "Disable this wizard"
msgstr "تعطيل هذا المعالج"

#: inc/config/theme-setup-wizard.php:34
msgid "Skip"
msgstr "تخطي"

#: inc/config/theme-setup-wizard.php:35 woocommerce/myaccount/orders.php:105
msgid "Next"
msgstr "التالي"

#: inc/config/theme-setup-wizard.php:36
msgid "Start"
msgstr "ابدأ"

#: inc/config/theme-setup-wizard.php:37
msgid "Cancel"
msgstr "إلغاء"

#: inc/config/theme-setup-wizard.php:38 inc/config/theme-setup-wizard.php:39
#: inc/config/theme-setup-wizard.php:40
#: inc/tgm/class-tgm-plugin-activation.php:2867
msgid "Install"
msgstr "تثبيت"

#: inc/config/theme-setup-wizard.php:41
msgid "Import"
msgstr "استيراد"

#: inc/config/theme-setup-wizard.php:42
#: inc/tgm/class-tgm-plugin-activation.php:2876
msgid "Activate"
msgstr "تفعيل"

#: inc/config/theme-setup-wizard.php:43
msgid "Later"
msgstr "لاحقاً"

#: inc/config/theme-setup-wizard.php:46
#. translators: Theme Name
msgid "Activate %s"
msgstr "تفعيل %s"

#: inc/config/theme-setup-wizard.php:48
#. translators: Theme Name
msgid "%s is Activated"
msgstr "%s مفعل"

#: inc/config/theme-setup-wizard.php:50
#. translators: Theme Name
msgid "Enter your license key to enable remote updates and theme support."
msgstr "أدخل مفتاح الترخيص لتمكين التحديثات البعيدة ودعم القالب."

#: inc/config/theme-setup-wizard.php:51
msgid "License key"
msgstr "مفتاح الترخيص"

#: inc/config/theme-setup-wizard.php:52
msgid "The theme is already registered, so you can go to the next step!"
msgstr "القالب مسجل بالفعل، لذا يمكنك الانتقال إلى الخطوة التالية!"

#: inc/config/theme-setup-wizard.php:53
msgid "Your theme is activated! Remote updates and theme support are enabled."
msgstr "تم تفعيل قالبك! التحديثات البعيدة ودعم القالب مفعلان."

#: inc/config/theme-setup-wizard.php:54
msgid "Need help?"
msgstr "تحتاج مساعدة؟"

#: inc/config/theme-setup-wizard.php:57
#. translators: Theme Name
msgid "Welcome to %s"
msgstr "مرحباً بك في %s"

#: inc/config/theme-setup-wizard.php:58
msgid "Hi. Welcome back"
msgstr "مرحباً. أهلاً بعودتك"

#: inc/config/theme-setup-wizard.php:59
msgid ""
"This wizard will set up your theme, install plugins, and import content. It "
"is optional & should take only a few minutes."
msgstr ""
"سيقوم هذا المعالج بإعداد قالبك وتثبيت الإضافات واستيراد المحتوى. إنه اختياري "
"ولن يستغرق سوى بضع دقائق."

#: inc/config/theme-setup-wizard.php:60
msgid ""
"You may have already run this theme setup wizard. If you would like to "
"proceed anyway, click on the \"Start\" button below."
msgstr ""
"ربما تكون قد شغلت معالج إعداد القالب هذا بالفعل. إذا كنت تريد المتابعة على أي حال، "
"انقر على زر \"ابدأ\" أدناه."

#: inc/config/theme-setup-wizard.php:62
msgid "Install Child Theme"
msgstr "تثبيت القالب الفرعي"

#: inc/config/theme-setup-wizard.php:63
msgid "You're good to go!"
msgstr "أنت جاهز للانطلاق!"

#: inc/config/theme-setup-wizard.php:64
msgid "Let's build & activate a child theme so you may easily make theme changes."
msgstr "دعنا ننشئ ونفعل قالباً فرعياً حتى تتمكن من إجراء تغييرات على القالب بسهولة."

#: inc/config/theme-setup-wizard.php:65
msgid ""
"Your child theme has already been installed and is now activated, if it "
"wasn't already."
msgstr ""
"تم تثبيت قالبك الفرعي بالفعل وهو مفعل الآن، إذا لم يكن كذلك من قبل."

#: inc/config/theme-setup-wizard.php:66
msgid "Learn about child themes"
msgstr "تعلم عن القوالب الفرعية"

#: inc/config/theme-setup-wizard.php:67
msgid "Awesome. Your child theme has already been installed and is now activated."
msgstr "رائع. تم تثبيت قالبك الفرعي بالفعل وهو مفعل الآن."

#: inc/config/theme-setup-wizard.php:68
msgid "Awesome. Your child theme has been created and is now activated."
msgstr "رائع. تم إنشاء قالبك الفرعي وهو مفعل الآن."

#: inc/config/theme-setup-wizard.php:70
#: inc/tgm/class-tgm-plugin-activation.php:341
msgid "Install Plugins"
msgstr "تثبيت الإضافات"

#: inc/config/theme-setup-wizard.php:71
msgid "You're up to speed!"
msgstr "أنت محدث!"

#: inc/config/theme-setup-wizard.php:72
msgid "Let's install some essential WordPress plugins to get your site up to speed."
msgstr "دعنا نثبت بعض إضافات ووردبريس الأساسية لتسريع موقعك."

#: inc/config/theme-setup-wizard.php:73
msgid ""
"The required WordPress plugins are all installed and up to date. Press "
"\"Next\" to continue the setup wizard."
msgstr ""
"جميع إضافات ووردبريس المطلوبة مثبتة ومحدثة. اضغط على \"التالي\" لمتابعة معالج الإعداد."

#: inc/config/theme-setup-wizard.php:74 inc/config/theme-setup-wizard.php:78
#: inc/customizer/panels/panels.php:54
msgid "Advanced"
msgstr "متقدم"

#: inc/config/theme-setup-wizard.php:76
msgid "Import Content"
msgstr "استيراد المحتوى"

#: inc/config/theme-setup-wizard.php:77
msgid ""
"When creating a website from scratch, you should import \"Extra Data\"; "
"otherwise, import only \"Essential Data\"."
msgstr ""
"عند إنشاء موقع ويب من الصفر، يجب استيراد \"البيانات الإضافية\"؛ "
"وإلا، استورد \"البيانات الأساسية\" فقط."

#: inc/config/theme-setup-wizard.php:80
msgid "All done. Have fun!"
msgstr "تم الانتهاء من كل شيء. استمتع!"

#: inc/config/theme-setup-wizard.php:83
#. translators: Theme Author
msgid "Your theme has been all set up. Enjoy your new theme by %s."
msgstr "تم إعداد قالبك بالكامل. استمتع بقالبك الجديد من %s."

#: inc/config/theme-setup-wizard.php:84
msgid "Extras"
msgstr "إضافات"

#: inc/config/theme-setup-wizard.php:85
msgid "Explore WordPress"
msgstr "استكشف ووردبريس"

#: inc/config/theme-setup-wizard.php:86
msgid "Get Theme Support"
msgstr "احصل على دعم القالب"

#: inc/config/theme-setup-wizard.php:87
msgid "Start Customizing"
msgstr "ابدأ التخصيص"

#: inc/config/theme-sidebars.php:12
msgid "Top Bar"
msgstr "الشريط العلوي"

#: inc/config/theme-sidebars.php:15 inc/config/theme-sidebars.php:25
#: inc/config/theme-sidebars.php:35 inc/config/theme-sidebars.php:55
msgid "Add widgets here."
msgstr "أضف الودجات هنا."

#: inc/config/theme-sidebars.php:22
msgid "Sidebar Blog Top"
msgstr "الشريط الجانبي أعلى المدونة"

#: inc/config/theme-sidebars.php:32
msgid "Sidebar Blog"
msgstr "الشريط الجانبي للمدونة"

#: inc/config/theme-sidebars.php:42
msgid "Sidebar Shop Filter"
msgstr "مرشح الشريط الجانبي للمتجر"

#: inc/config/theme-sidebars.php:45
msgid "Add shop filter widgets here."
msgstr "أضف ودجات مرشح المتجر هنا."

#: inc/config/theme-sidebars.php:52
msgid "Product Category Bottom"
msgstr "أسفل فئة المنتج"

#: inc/customizer/panels/panels.php:18 inc/customizer/sections/blog.php:93
#: inc/customizer/sections/blog.php:228 inc/customizer/sections/general.php:36
#: inc/customizer/sections/logo.php:32
msgid "General"
msgstr "عام"

#: inc/customizer/panels/panels.php:19
msgid "General theme settings"
msgstr "إعدادات القالب العامة"

#: inc/customizer/panels/panels.php:30 inc/customizer/sections/blog.php:31
#: inc/customizer/sections/blog.php:42 inc/customizer/sections/blog.php:163
#: inc/customizer/sections/blog.php:174 inc/customizer/sections/footer.php:33
#: inc/customizer/sections/footer.php:47
#: inc/customizer/sections/woocommerce.php:162
#: inc/customizer/sections/woocommerce.php:1198
#: inc/customizer/sections/woocommerce.php:1228
msgid "Layout"
msgstr "التخطيط"

#: inc/customizer/panels/panels.php:31
msgid "The layout configuration"
msgstr "تكوين التخطيط"

#: inc/customizer/panels/panels.php:42
msgid "Components"
msgstr "المكونات"

#: inc/customizer/panels/panels.php:43
msgid "Other components"
msgstr "مكونات أخرى"

#: inc/customizer/panels/panels.php:55
msgid "Advanced Configuration"
msgstr "التكوين المتقدم"

#: inc/customizer/panels/panels.php:66 inc/helper/class-tag.php:481
msgid "Blog"
msgstr "المدونة"

#: inc/customizer/panels/panels.php:67
msgid "Blog Configuration"
msgstr "تكوين المدونة"

#: inc/customizer/sections/account.php:18
#: inc/customizer/sections/woocommerce.php:19
#: template-parts/components/mobile-navigation.php:46
#: template-parts/headers/default.php:94
#: template-parts/headers/header-02.php:124
#: template-parts/headers/header-03.php:108
#: template-parts/headers/header-04.php:83
#: template-parts/headers/header-05.php:83
#: template-parts/headers/header-06.php:83
#: template-parts/headers/header-07.php:87
#: template-parts/headers/header-08.php:88
msgid "Account"
msgstr "الحساب"

#: inc/customizer/sections/account.php:20
msgid "This section contains advanced all configurations for Account."
msgstr "يحتوي هذا القسم على جميع التكوينات المتقدمة للحساب."

#: inc/customizer/sections/account.php:31
msgid "Header Account"
msgstr "حساب الرأس"

#: inc/customizer/sections/account.php:41 inc/customizer/sections/search.php:45
msgid "Popup"
msgstr "نافذة منبثقة"

#: inc/customizer/sections/account.php:42
msgid "Show login/register form as popup."
msgstr "عرض نموذج تسجيل الدخول/التسجيل كنافذة منبثقة."

#: inc/customizer/sections/account.php:53 inc/customizer/sections/rating.php:48
#: inc/customizer/sections/scroll-to-top.php:56
#: inc/customizer/sections/search.php:142
#: inc/customizer/sections/woocommerce.php:867
msgid "Icon"
msgstr "أيقونة"

#: inc/customizer/sections/account.php:54
#: inc/customizer/sections/woocommerce.php:597
#: inc/customizer/sections/woocommerce.php:798
msgid "Choose the wish list icon ?"
msgstr "اختر أيقونة قائمة الأمنيات؟"

#: inc/customizer/sections/account.php:67 inc/customizer/sections/rating.php:62
#: inc/customizer/sections/scroll-to-top.php:88
#: inc/customizer/sections/search.php:156
msgid "Icon Size"
msgstr "حجم الأيقونة"

#: inc/customizer/sections/account.php:68
#: inc/customizer/sections/account.php:101
#: inc/customizer/sections/account.php:176
#: inc/customizer/sections/search.php:157
#: inc/customizer/sections/search.php:191
#: inc/customizer/sections/search.php:271
#: inc/customizer/sections/woocommerce.php:194
#: inc/customizer/sections/woocommerce.php:225
#: inc/customizer/sections/woocommerce.php:338
#: inc/customizer/sections/woocommerce.php:378
#: inc/customizer/sections/woocommerce.php:396
#: inc/customizer/sections/woocommerce.php:444
#: inc/customizer/sections/woocommerce.php:611
#: inc/customizer/sections/woocommerce.php:644
#: inc/customizer/sections/woocommerce.php:812
msgid "Unit : Pixel"
msgstr "الوحدة : بكسل"

#: inc/customizer/sections/account.php:85
#: inc/customizer/sections/scroll-to-top.php:113
#: inc/customizer/sections/search.php:174
msgid "Icon Color"
msgstr "لون الأيقونة"

#: inc/customizer/sections/account.php:100
#: inc/customizer/sections/search.php:190
msgid "Icon Border"
msgstr "حدود الأيقونة"

#: inc/customizer/sections/account.php:118
#: inc/customizer/sections/search.php:224
msgid "Icon Border Radius"
msgstr "نصف قطر حدود الأيقونة"

#: inc/customizer/sections/account.php:119
#: inc/customizer/sections/account.php:216
#: inc/customizer/sections/search.php:225
#: inc/customizer/sections/search.php:311
#: inc/customizer/sections/woocommerce.php:662
msgid ""
"Control <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"border radius</a>."
msgstr ""
"التحكم في <a target=\"_blank\" "
"href=\"https://developer.mozilla.org/en-US/docs/Web/CSS/border-radius\"> "
"نصف قطر الحدود</a>."

#: inc/customizer/sections/account.php:138
#: inc/customizer/sections/search.php:244
msgid "Icon Border Color"
msgstr "لون حدود الأيقونة"

#: inc/customizer/sections/account.php:160
msgid "Content Background Color"
msgstr "لون خلفية المحتوى"

#: inc/customizer/sections/account.php:175
#: inc/customizer/sections/search.php:270
msgid "Input Border"
msgstr "حدود الإدخال"

#: inc/customizer/sections/account.php:193
#: inc/customizer/sections/search.php:288
msgid "Input Border Color"
msgstr "لون حدود الإدخال"

#: inc/customizer/sections/account.php:215
#: inc/customizer/sections/search.php:310
msgid "Input Border Radius"
msgstr "نصف قطر حدود الإدخال"

#: inc/customizer/sections/account.php:234
msgid "Show Edit Account"
msgstr "عرض تحرير الحساب"

#: inc/customizer/sections/account.php:235
msgid "Show/Hide edit account button."
msgstr "إظهار/إخفاء زر تحرير الحساب."

#: inc/customizer/sections/account.php:248
msgid "Edit Account Icon"
msgstr "أيقونة تحرير الحساب"

#: inc/customizer/sections/account.php:249
#: inc/customizer/sections/account.php:308
#: inc/customizer/sections/scroll-to-top.php:57
#: inc/customizer/sections/woocommerce.php:868
msgid "Choose the icon ?"
msgstr "اختر الأيقونة؟"

#: inc/customizer/sections/account.php:269
msgid "Edit Account Icon Color"
msgstr "لون أيقونة تحرير الحساب"

#: inc/customizer/sections/account.php:290
msgid "Show Logout"
msgstr "عرض تسجيل الخروج"

#: inc/customizer/sections/account.php:291
msgid "Show/Hide logout button."
msgstr "إظهار/إخفاء زر تسجيل الخروج."

#: inc/customizer/sections/account.php:307
#: inc/customizer/sections/woocommerce.php:112
msgid "Logout Icon"
msgstr "أيقونة تسجيل الخروج"

#: inc/customizer/sections/account.php:328
msgid "Logout Icon Color"
msgstr "لون أيقونة تسجيل الخروج"

#: inc/customizer/sections/advanced.php:17
msgid "Mega Menu"
msgstr "القائمة الضخمة"

#: inc/customizer/sections/advanced.php:19
msgid "This section contains advanced configurations."
msgstr "يحتوي هذا القسم على التكوينات المتقدمة."

#: inc/customizer/sections/advanced.php:30
msgid "Normalize Classes"
msgstr "تطبيع الفئات"

#: inc/customizer/sections/advanced.php:31
msgid "Remove unnecessary css classes of menu item."
msgstr "إزالة فئات CSS غير الضرورية من عنصر القائمة."

#: inc/customizer/sections/advanced.php:45
#: inc/customizer/sections/advanced.php:57
msgid "404 Error Page"
msgstr "صفحة خطأ 404"

#: inc/customizer/sections/advanced.php:47
msgid "404 Error Page Configuration."
msgstr "تكوين صفحة خطأ 404."

#: inc/customizer/sections/advanced.php:66
msgid "Image"
msgstr "صورة"

#: inc/customizer/sections/advanced.php:80
#: inc/customizer/sections/promotion.php:132
#: inc/customizer/sections/woocommerce.php:536
#: inc/customizer/sections/woocommerce.php:1297
#: inc/customizer/sections/woocommerce.php:1381
msgid "Title"
msgstr "العنوان"

#: inc/customizer/sections/advanced.php:93
#: inc/customizer/sections/promotion.php:180
msgid "Description"
msgstr "الوصف"

#: inc/customizer/sections/blog.php:19
msgid "Blog Category"
msgstr "فئة المدونة"

#: inc/customizer/sections/blog.php:21
msgid "This section contains blog category options."
msgstr "يحتوي هذا القسم على خيارات فئة المدونة."

#: inc/customizer/sections/blog.php:45 inc/customizer/sections/blog.php:177
#: inc/customizer/sections/woocommerce.php:166
#: inc/customizer/sections/woocommerce.php:1202
msgid "Select a layout..."
msgstr "اختر تخطيطاً..."

#: inc/customizer/sections/blog.php:56
msgid "Columns"
msgstr "الأعمدة"

#: inc/customizer/sections/blog.php:73 inc/customizer/sections/blog.php:188
msgid "Sidebar"
msgstr "الشريط الجانبي"

#: inc/customizer/sections/blog.php:76 inc/customizer/sections/blog.php:191
msgid "Choose sidebar..."
msgstr "اختر الشريط الجانبي..."

#: inc/customizer/sections/blog.php:102
msgid "Show author"
msgstr "عرض المؤلف"

#: inc/customizer/sections/blog.php:114 inc/customizer/sections/blog.php:249
msgid "Show Publish Date"
msgstr "عرض تاريخ النشر"

#: inc/customizer/sections/blog.php:126
msgid "Show excerpt"
msgstr "عرض المقتطف"

#: inc/customizer/sections/blog.php:138
msgid "Show comment count"
msgstr "عرض عدد التعليقات"

#: inc/customizer/sections/blog.php:150
msgid "Blog Detail"
msgstr "تفاصيل المدونة"

#: inc/customizer/sections/blog.php:152
msgid "This section contains blog detail options."
msgstr "يحتوي هذا القسم على خيارات تفاصيل المدونة."

#: inc/customizer/sections/blog.php:206
msgid "Feature Image Position"
msgstr "موضع الصورة المميزة"

#: inc/customizer/sections/blog.php:209
#: inc/customizer/sections/woocommerce.php:1503
msgid "Select a position..."
msgstr "اختر موضعاً..."

#: inc/customizer/sections/blog.php:237
msgid "Show Author"
msgstr "عرض المؤلف"

#: inc/customizer/sections/blog.php:261
msgid "Show Category"
msgstr "عرض الفئة"

#: inc/customizer/sections/blog.php:273
msgid "Show Tag"
msgstr "عرض الوسم"

#: inc/customizer/sections/blog.php:285
msgid "Show Comment"
msgstr "عرض التعليق"

#: inc/customizer/sections/blog.php:297
msgid "Show Comment Form"
msgstr "عرض نموذج التعليق"

#: inc/customizer/sections/blog.php:309
msgid "Show Social Share"
msgstr "عرض المشاركة الاجتماعية"

#: inc/customizer/sections/blog.php:323
#: template-parts/contents/related-posts.php:57
msgid "Related Posts"
msgstr "المقالات ذات الصلة"

#: inc/customizer/sections/blog.php:332
msgid "Show Related Posts"
msgstr "عرض المقالات ذات الصلة"

#: inc/customizer/sections/blog.php:344
msgid "Show Navigation"
msgstr "عرض التنقل"

#: inc/customizer/sections/blog.php:356
msgid "Show Pagination"
msgstr "عرض ترقيم الصفحات"

#: inc/customizer/sections/blog.php:368
msgid "Auto Play"
msgstr "التشغيل التلقائي"

#: inc/customizer/sections/blog.php:380
msgid "Auto Play Speed"
msgstr "سرعة التشغيل التلقائي"

#: inc/customizer/sections/footer.php:16
msgid "Footer"
msgstr "التذييل"

#: inc/customizer/sections/footer.php:17
msgid "Theme footer."
msgstr "تذييل القالب."

#: inc/customizer/sections/footer.php:50
msgid "Select a footer..."
msgstr "اختر تذييلاً..."

#: inc/customizer/sections/general.php:21
msgid "Colors"
msgstr "الألوان"

#: inc/customizer/sections/general.php:22
msgid "General colors."
msgstr "الألوان العامة."

#: inc/customizer/sections/general.php:45
msgid "Heading Color"
msgstr "لون العنوان"

#: inc/customizer/sections/general.php:46
msgid "H1, H2 ... H6"
msgstr "H1, H2 ... H6"

#: inc/customizer/sections/general.php:61
msgid "Body Text"
msgstr "نص المحتوى"

#: inc/customizer/sections/general.php:62
msgid "Set the color for the body text."
msgstr "تعيين لون نص المحتوى."

#: inc/customizer/sections/general.php:77
#: inc/customizer/sections/header.php:214
msgid "Background color"
msgstr "لون الخلفية"

#: inc/customizer/sections/general.php:78
msgid "Visible if the grid is contained."
msgstr "مرئي إذا كانت الشبكة محتواة."

#: inc/customizer/sections/general.php:93
msgid "Primary color"
msgstr "اللون الأساسي"

#: inc/customizer/sections/general.php:108
msgid "Secondary color"
msgstr "اللون الثانوي"

#: inc/customizer/sections/general.php:127
#: inc/customizer/sections/promotion.php:206
msgid "Link"
msgstr "رابط"

#: inc/customizer/sections/general.php:136
msgid "Choose color"
msgstr "اختر لوناً"

#: inc/customizer/sections/general.php:137
msgid "This is a color of the link."
msgstr "هذا لون الرابط."

#: inc/customizer/sections/general.php:142
#: inc/customizer/sections/general.php:172
#: inc/customizer/sections/general.php:189
msgid "Default"
msgstr "افتراضي"

#: inc/customizer/sections/general.php:143
#: inc/customizer/sections/general.php:173
#: inc/customizer/sections/general.php:190
msgid "Hover"
msgstr "تمرير"

#: inc/customizer/sections/general.php:157
msgid "General Button"
msgstr "الزر العام"

#: inc/customizer/sections/general.php:166
#: inc/customizer/sections/mobile-navigation.php:73
msgid "Text Color"
msgstr "لون النص"

#: inc/customizer/sections/general.php:167
msgid "This is a color of text."
msgstr "هذا لون النص."

#: inc/customizer/sections/general.php:183
#: inc/customizer/sections/mobile-navigation.php:51
#: inc/customizer/sections/modal.php:43
#: inc/customizer/sections/scroll-to-top.php:135
#: inc/customizer/sections/title.php:142
msgid "Background Color"
msgstr "لون الخلفية"

#: inc/customizer/sections/general.php:184
msgid "This is a color of background button."
msgstr "هذا لون خلفية الزر."
